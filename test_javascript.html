<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار JavaScript / JavaScript Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .debug {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار JavaScript / JavaScript Test</h1>
        <hr>
        
        <div>
            <button onclick="testAppRoot()">اختبار appRoot</button>
            <button onclick="testHandleLogin()">اختبار handleLogin</button>
            <button onclick="testFormSubmission()">اختبار إرسال النموذج</button>
            <button onclick="clearResults()">مسح النتائج</button>
        </div>
        
        <div id="results"></div>
        
        <!-- تضمين ملفات JavaScript الأصلية -->
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.1.1/jquery.min.js"></script>
        <script>
            // نسخة محدثة من setAppRoot
            function setAppRoot(devFolderName, prodFolderName){
                var hostname = window.location.hostname;
                
                //attach trailing slash to both foldernames
                var devFolder = devFolderName ? devFolderName+"/" : "";
                var prodFolder = prodFolderName ? prodFolderName+"/" : "";
                
                var baseURL = "";
                
                if(hostname.search("localhost") !== -1 || (hostname.search("192.168.") !== -1)  || (hostname.search("127.0.0.") !== -1)){
                    baseURL = window.location.origin+"/"+devFolder;
                }
                else{
                    baseURL = window.location.origin+"/"+prodFolder;
                }
                
                return baseURL;
            }
            
            // تعيين appRoot المحدث
            var appRoot = setAppRoot("", "");
            
            // نسخة من handleLogin
            function handleLogin(email, password, callback){
                var jsonToReturn = "";
                
                $.ajax(appRoot+'access/login', {
                    method: "POST",
                    data: {email:email, password:password}
                }).done(function(returnedData){
                    if(returnedData.status === 1){
                        jsonToReturn = {status:1, msg:"Authenticated..."};
                    }
                    else{
                        //display error messages
                        jsonToReturn = {status:0, msg:"Invalid email/password combination"};
                    }
                    
                    typeof(callback) === "function" ? callback(jsonToReturn) : "";
                    
                }).fail(function(xhr){
                    //set error message based on the internet connectivity of the user
                    var msg = xhr.status+' '+xhr.statusText;
                    
                    //display error messages
                    jsonToReturn = {status:0, msg:msg};
                    
                    typeof(callback) === "function" ? callback(jsonToReturn) : "";
                });
            }
            
            // دالة اختبار appRoot
            function testAppRoot() {
                addResult('info', 'اختبار appRoot / Testing appRoot');
                addResult('info', 'Current hostname: ' + window.location.hostname);
                addResult('info', 'Current origin: ' + window.location.origin);
                addResult('info', 'Calculated appRoot: ' + appRoot);
                
                // اختبار المسارات
                var testRoutes = ['access/login', 'access/css', 'dashboard'];
                testRoutes.forEach(function(route) {
                    var fullUrl = appRoot + route;
                    addResult('info', 'Route: ' + route + ' -> ' + fullUrl);
                });
            }
            
            // دالة اختبار handleLogin
            function testHandleLogin() {
                addResult('info', 'اختبار handleLogin / Testing handleLogin');
                
                handleLogin('<EMAIL>', '12345678', function(result) {
                    if (result.status === 1) {
                        addResult('success', '✅ تم تسجيل الدخول بنجاح / Login successful');
                    } else {
                        addResult('error', '❌ فشل تسجيل الدخول / Login failed: ' + result.msg);
                    }
                    addResult('debug', 'Response: ' + JSON.stringify(result, null, 2));
                });
            }
            
            // دالة اختبار إرسال النموذج
            function testFormSubmission() {
                addResult('info', 'اختبار إرسال النموذج / Testing form submission');
                
                // محاكاة إرسال النموذج
                var email = '<EMAIL>';
                var password = '12345678';
                
                if(!email || !password){
                    var errMsg = !email ? "Enter your email" : "Enter your password";
                    addResult('error', errMsg);
                    return;
                }
                
                addResult('info', 'Authenticating...');
                
                //call function to handle log in and get the returned data through a callback
                handleLogin(email, password, function(returnedData){
                   if(returnedData.status === 1){
                        addResult('success', "Authenticated. Redirecting....");
                        
                        // محاكاة إعادة التوجيه
                        setTimeout(function() {
                            addResult('info', 'Redirecting to: ' + appRoot + 'dashboard');
                        }, 1000);
                    }
                    else{
                        //display error message
                        addResult('error', returnedData.msg);
                    }
                });
            }
            
            function clearResults() {
                document.getElementById('results').innerHTML = '';
            }
            
            function addResult(type, message) {
                const resultsDiv = document.getElementById('results');
                const resultDiv = document.createElement('div');
                resultDiv.className = `result ${type}`;
                resultDiv.innerHTML = '<strong>' + message + '</strong>';
                resultsDiv.appendChild(resultDiv);
                
                // التمرير إلى النتيجة الجديدة
                resultDiv.scrollIntoView({ behavior: 'smooth' });
            }
            
            // اختبار تلقائي عند تحميل الصفحة
            $(document).ready(function() {
                addResult('info', 'تم تحميل الصفحة / Page loaded');
                addResult('info', 'jQuery version: ' + $.fn.jquery);
                testAppRoot();
            });
        </script>
    </div>
</body>
</html>
