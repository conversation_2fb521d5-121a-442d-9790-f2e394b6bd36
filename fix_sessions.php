<?php
/**
 * ملف إصلاح مشاكل الجلسات
 * Session Issues Fix Script
 */

echo "<h2>إصلاح مشاكل الجلسات / Session Issues Fix</h2>";
echo "<hr>";

// 1. إنشاء مجلد الجلسات
echo "<h3>1. إنشاء مجلد الجلسات / Create Sessions Directory</h3>";

$sessionPath = dirname(__FILE__) . '/system/sessions';
echo "مسار الجلسات / Session Path: <strong>$sessionPath</strong><br>";

if (!is_dir($sessionPath)) {
    if (mkdir($sessionPath, 0755, true)) {
        echo "<span style='color: green;'>✅ تم إنشاء مجلد الجلسات بنجاح / Sessions directory created successfully</span><br>";
    } else {
        echo "<span style='color: red;'>❌ فشل في إنشاء مجلد الجلسات / Failed to create sessions directory</span><br>";
    }
} else {
    echo "<span style='color: green;'>✅ مجلد الجلسات موجود / Sessions directory exists</span><br>";
}

// 2. فحص وإصلاح الصلاحيات
echo "<h3>2. فحص الصلاحيات / Check Permissions</h3>";

if (is_dir($sessionPath)) {
    $perms = fileperms($sessionPath);
    $permsOctal = substr(sprintf('%o', $perms), -4);
    echo "صلاحيات المجلد الحالية / Current permissions: <strong>$permsOctal</strong><br>";
    
    if (is_writable($sessionPath)) {
        echo "<span style='color: green;'>✅ المجلد قابل للكتابة / Directory is writable</span><br>";
    } else {
        echo "<span style='color: red;'>❌ المجلد غير قابل للكتابة / Directory is not writable</span><br>";
        
        if (chmod($sessionPath, 0755)) {
            echo "<span style='color: green;'>✅ تم إصلاح الصلاحيات / Permissions fixed</span><br>";
        } else {
            echo "<span style='color: red;'>❌ فشل في إصلاح الصلاحيات / Failed to fix permissions</span><br>";
            echo "<strong>قم بتشغيل هذا الأمر يدوياً / Run this command manually:</strong><br>";
            echo "<code>chmod 755 $sessionPath</code><br>";
        }
    }
}

// 3. إنشاء ملف index.html للحماية
echo "<h3>3. إنشاء ملف الحماية / Create Protection File</h3>";

$indexFile = $sessionPath . '/index.html';
$indexContent = '<!DOCTYPE html>
<html>
<head>
    <title>403 Forbidden</title>
</head>
<body>
    <p>Directory access is forbidden.</p>
</body>
</html>';

if (!file_exists($indexFile)) {
    if (file_put_contents($indexFile, $indexContent)) {
        echo "<span style='color: green;'>✅ تم إنشاء ملف الحماية / Protection file created</span><br>";
    } else {
        echo "<span style='color: red;'>❌ فشل في إنشاء ملف الحماية / Failed to create protection file</span><br>";
    }
} else {
    echo "<span style='color: green;'>✅ ملف الحماية موجود / Protection file exists</span><br>";
}

// 4. اختبار كتابة الجلسات
echo "<h3>4. اختبار كتابة الجلسات / Test Session Writing</h3>";

session_start();
$_SESSION['test'] = 'session_test_' . time();
$sessionId = session_id();

echo "معرف الجلسة / Session ID: <strong>$sessionId</strong><br>";

$sessionFile = $sessionPath . '/sess_' . $sessionId;
if (file_exists($sessionFile)) {
    echo "<span style='color: green;'>✅ ملف الجلسة تم إنشاؤه بنجاح / Session file created successfully</span><br>";
    echo "مسار ملف الجلسة / Session file path: <strong>$sessionFile</strong><br>";
    
    $sessionContent = file_get_contents($sessionFile);
    echo "محتوى ملف الجلسة / Session file content: <code>$sessionContent</code><br>";
} else {
    echo "<span style='color: red;'>❌ لم يتم إنشاء ملف الجلسة / Session file not created</span><br>";
}

// 5. تنظيف الجلسات القديمة
echo "<h3>5. تنظيف الجلسات القديمة / Clean Old Sessions</h3>";

if (is_dir($sessionPath)) {
    $files = glob($sessionPath . '/sess_*');
    $oldFiles = 0;
    $currentTime = time();
    
    foreach ($files as $file) {
        if (is_file($file)) {
            $fileTime = filemtime($file);
            // حذف الملفات الأقدم من 24 ساعة
            if (($currentTime - $fileTime) > 86400) {
                if (unlink($file)) {
                    $oldFiles++;
                }
            }
        }
    }
    
    echo "تم حذف $oldFiles ملف جلسة قديم / Deleted $oldFiles old session files<br>";
    
    $remainingFiles = count(glob($sessionPath . '/sess_*'));
    echo "عدد ملفات الجلسات المتبقية / Remaining session files: <strong>$remainingFiles</strong><br>";
}

// 6. فحص إعدادات PHP
echo "<h3>6. فحص إعدادات PHP / Check PHP Settings</h3>";

$phpIssues = [];

if (ini_get('session.auto_start') == '1') {
    $phpIssues[] = 'session.auto_start يجب أن يكون 0';
}

if (ini_get('session.use_cookies') != '1') {
    $phpIssues[] = 'session.use_cookies يجب أن يكون 1';
}

if (ini_get('session.use_only_cookies') != '1') {
    $phpIssues[] = 'session.use_only_cookies يجب أن يكون 1';
}

if (empty($phpIssues)) {
    echo "<span style='color: green;'>✅ جميع إعدادات PHP صحيحة / All PHP settings are correct</span><br>";
} else {
    echo "<span style='color: red;'>❌ مشاكل في إعدادات PHP / PHP settings issues:</span><br>";
    foreach ($phpIssues as $issue) {
        echo "- $issue<br>";
    }
}

// 7. إنشاء ملف .htaccess للحماية
echo "<h3>7. إنشاء ملف .htaccess / Create .htaccess File</h3>";

$htaccessFile = $sessionPath . '/.htaccess';
$htaccessContent = 'Order deny,allow
Deny from all';

if (!file_exists($htaccessFile)) {
    if (file_put_contents($htaccessFile, $htaccessContent)) {
        echo "<span style='color: green;'>✅ تم إنشاء ملف .htaccess / .htaccess file created</span><br>";
    } else {
        echo "<span style='color: orange;'>⚠️ لم يتم إنشاء ملف .htaccess (قد لا يكون ضرورياً) / .htaccess file not created (may not be necessary)</span><br>";
    }
} else {
    echo "<span style='color: green;'>✅ ملف .htaccess موجود / .htaccess file exists</span><br>";
}

// 8. اختبار شامل للجلسات
echo "<h3>8. اختبار شامل للجلسات / Comprehensive Session Test</h3>";

// إعادة تعيين الجلسة
session_destroy();
session_start();

$testData = [
    'admin_id' => 1,
    'admin_email' => '<EMAIL>',
    'admin_role' => 'Super',
    'admin_name' => 'Test Admin',
    'test_time' => time()
];

foreach ($testData as $key => $value) {
    $_SESSION[$key] = $value;
}

// التحقق من حفظ البيانات
$allSaved = true;
foreach ($testData as $key => $value) {
    if (!isset($_SESSION[$key]) || $_SESSION[$key] != $value) {
        $allSaved = false;
        break;
    }
}

if ($allSaved) {
    echo "<span style='color: green;'>✅ اختبار الجلسات نجح بالكامل / Session test completely successful</span><br>";
    echo "<strong>بيانات الجلسة المحفوظة / Saved session data:</strong><br>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    print_r($_SESSION);
    echo "</pre>";
} else {
    echo "<span style='color: red;'>❌ فشل اختبار الجلسات / Session test failed</span><br>";
}

// تنظيف بيانات الاختبار
foreach ($testData as $key => $value) {
    unset($_SESSION[$key]);
}

echo "<hr>";
echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin-top: 20px;'>";
echo "<strong>ملخص الإصلاحات / Fix Summary:</strong><br>";
echo "1. ✅ تم إنشاء/فحص مجلد الجلسات<br>";
echo "2. ✅ تم فحص وإصلاح الصلاحيات<br>";
echo "3. ✅ تم إنشاء ملفات الحماية<br>";
echo "4. ✅ تم اختبار كتابة الجلسات<br>";
echo "5. ✅ تم تنظيف الجلسات القديمة<br>";
echo "<br>";
echo "<strong>الخطوة التالية / Next Step:</strong><br>";
echo "جرب تسجيل الدخول مرة أخرى في النظام<br>";
echo "Try logging in to the system again<br>";
echo "<br>";
echo "<strong>⚠️ احذف هذا الملف بعد حل المشكلة!</strong><br>";
echo "<strong>⚠️ Delete this file after fixing the issue!</strong><br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
table {
    background-color: white;
    margin: 10px 0;
}
th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
