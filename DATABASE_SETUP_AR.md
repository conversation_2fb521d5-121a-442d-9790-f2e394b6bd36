# إعداد قاعدة البيانات - نظام إدارة المخزون والمبيعات

## معلومات قاعدة البيانات

تم تحديث النظام للاتصال بقاعدة البيانات التالية:

- **اسم قاعدة البيانات**: `kidzrcle_MBEAAAT`
- **اسم المستخدم**: `kidzrcle_MBEAAAT`
- **كلمة المرور**: `kidzrcle_MBEAAAT`
- **الخادم**: `localhost`

## خطوات الإعداد

### 1. إنشاء قاعدة البيانات

قم بتسجيل الدخول إلى phpMyAdmin أو MySQL وقم بإنشاء قاعدة بيانات جديدة:

```sql
CREATE DATABASE kidzrcle_MBEAAAT;
```

### 2. استيراد البيانات

قم باستيراد ملف `1410inventory.sql` إلى قاعدة البيانات الجديدة:

1. افتح phpMyAdmin
2. اختر قاعدة البيانات `kidzrcle_MBEAAAT`
3. انقر على تبويب "استيراد" (Import)
4. اختر ملف `1410inventory.sql`
5. انقر على "تنفيذ" (Go)

### 3. إنشاء المستخدم (اختياري)

إذا كنت تريد إنشاء مستخدم منفصل لقاعدة البيانات:

```sql
CREATE USER 'kidzrcle_MBEAAAT'@'localhost' IDENTIFIED BY 'kidzrcle_MBEAAAT';
GRANT ALL PRIVILEGES ON kidzrcle_MBEAAAT.* TO 'kidzrcle_MBEAAAT'@'localhost';
FLUSH PRIVILEGES;
```

## الملفات المحدثة

تم تحديث الملفات التالية:

1. `application/config/database.php` - إعدادات الاتصال بقاعدة البيانات
2. `1410inventory.sql` - ملف SQL محدث باسم قاعدة البيانات الجديد

## بيانات الدخول الافتراضية

بعد استيراد قاعدة البيانات، يمكنك تسجيل الدخول باستخدام:

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: demo (كلمة المرور مشفرة في قاعدة البيانات)

## هيكل قاعدة البيانات

تحتوي قاعدة البيانات على الجداول التالية:

1. **admin** - بيانات المديرين والمستخدمين
2. **items** - بيانات المنتجات والمخزون
3. **transactions** - بيانات المعاملات والمبيعات
4. **eventlog** - سجل الأحداث والعمليات
5. **lk_sess** - جلسات المستخدمين

## ملاحظات مهمة

- تأكد من أن خادم MySQL يعمل
- تأكد من أن PHP يدعم PDO MySQL
- تأكد من صحة بيانات الاتصال بقاعدة البيانات
- قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام

## استكشاف الأخطاء

إذا واجهت مشاكل في الاتصال:

1. تحقق من أن MySQL يعمل
2. تحقق من صحة اسم المستخدم وكلمة المرور
3. تحقق من أن قاعدة البيانات موجودة
4. تحقق من إعدادات الجدار الناري
5. راجع ملفات السجل للأخطاء
