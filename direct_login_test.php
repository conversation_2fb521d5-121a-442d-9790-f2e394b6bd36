<?php
/**
 * اختبار تسجيل الدخول المباشر
 * Direct Login Test
 */

// بدء الجلسة
session_start();

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'kidzrcle_MBEAAAT';
$username = 'kidzrcle_MBEAAAT';
$password = 'kidzrcle_MBEAAAT';

echo "<h2>اختبار تسجيل الدخول المباشر / Direct Login Test</h2>";
echo "<hr>";

// محاكاة طلب AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];
    
    echo "<h3>نتائج اختبار تسجيل الدخول / Login Test Results</h3>";
    echo "البريد الإلكتروني / Email: <strong>$email</strong><br>";
    echo "كلمة المرور / Password: <strong>$password</strong><br><br>";
    
    try {
        // الاتصال بقاعدة البيانات
        $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<span style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</span><br>";
        
        // جلب بيانات المدير
        $stmt = $pdo->prepare("SELECT id, first_name, last_name, email, password, role, account_status, deleted FROM admin WHERE email = ?");
        $stmt->execute([strtolower($email)]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<span style='color: green;'>✅ المستخدم موجود</span><br>";
            echo "معرف المستخدم / User ID: {$admin['id']}<br>";
            echo "الاسم / Name: {$admin['first_name']} {$admin['last_name']}<br>";
            echo "الدور / Role: {$admin['role']}<br>";
            echo "حالة الحساب / Account Status: {$admin['account_status']}<br>";
            echo "محذوف / Deleted: {$admin['deleted']}<br><br>";
            
            // فحص كلمة المرور
            $passwordMatch = password_verify($password, $admin['password']);
            echo "كلمة المرور صحيحة / Password correct: " . ($passwordMatch ? '<span style="color: green;">✅ نعم</span>' : '<span style="color: red;">❌ لا</span>') . "<br>";
            
            if (!$passwordMatch) {
                echo "<div style='color: red; background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<strong>مشكلة في كلمة المرور / Password Issue:</strong><br>";
                echo "كلمة المرور المحفوظة في قاعدة البيانات مشفرة بـ bcrypt<br>";
                echo "كلمة المرور المدخلة: $password<br>";
                echo "Hash في قاعدة البيانات: " . substr($admin['password'], 0, 30) . "...<br>";
                echo "</div>";
                
                // إصلاح كلمة المرور
                echo "<h4>إصلاح كلمة المرور / Fix Password</h4>";
                $newHash = password_hash($password, PASSWORD_BCRYPT);
                $updateStmt = $pdo->prepare("UPDATE admin SET password = ? WHERE email = ?");
                $updateResult = $updateStmt->execute([$newHash, $email]);
                
                if ($updateResult) {
                    echo "<span style='color: green;'>✅ تم تحديث كلمة المرور بنجاح</span><br>";
                    
                    // اختبار كلمة المرور الجديدة
                    $verifyNew = password_verify($password, $newHash);
                    echo "اختبار كلمة المرور الجديدة: " . ($verifyNew ? '<span style="color: green;">✅ نجح</span>' : '<span style="color: red;">❌ فشل</span>') . "<br>";
                    
                    if ($verifyNew) {
                        $passwordMatch = true;
                    }
                } else {
                    echo "<span style='color: red;'>❌ فشل في تحديث كلمة المرور</span><br>";
                }
            }
            
            // فحص حالة الحساب
            $accountActive = $admin['account_status'] == '1';
            echo "الحساب نشط / Account active: " . ($accountActive ? '<span style="color: green;">✅ نعم</span>' : '<span style="color: red;">❌ لا</span>') . "<br>";
            
            if (!$accountActive) {
                echo "<h4>تفعيل الحساب / Activate Account</h4>";
                $activateStmt = $pdo->prepare("UPDATE admin SET account_status = '1' WHERE email = ?");
                $activateResult = $activateStmt->execute([$email]);
                
                if ($activateResult) {
                    echo "<span style='color: green;'>✅ تم تفعيل الحساب</span><br>";
                    $accountActive = true;
                } else {
                    echo "<span style='color: red;'>❌ فشل في تفعيل الحساب</span><br>";
                }
            }
            
            // فحص حالة الحذف
            $notDeleted = $admin['deleted'] == '0';
            echo "الحساب غير محذوف / Not deleted: " . ($notDeleted ? '<span style="color: green;">✅ نعم</span>' : '<span style="color: red;">❌ لا</span>') . "<br>";
            
            if (!$notDeleted) {
                echo "<h4>إلغاء حذف الحساب / Undelete Account</h4>";
                $undeleteStmt = $pdo->prepare("UPDATE admin SET deleted = '0' WHERE email = ?");
                $undeleteResult = $undeleteStmt->execute([$email]);
                
                if ($undeleteResult) {
                    echo "<span style='color: green;'>✅ تم إلغاء حذف الحساب</span><br>";
                    $notDeleted = true;
                } else {
                    echo "<span style='color: red;'>❌ فشل في إلغاء حذف الحساب</span><br>";
                }
            }
            
            // النتيجة النهائية
            if ($passwordMatch && $accountActive && $notDeleted) {
                echo "<div style='color: green; background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>✅ نجح تسجيل الدخول! / Login Successful!</h4>";
                
                // إنشاء جلسة
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_email'] = $admin['email'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['admin_initial'] = strtoupper(substr($admin['first_name'], 0, 1));
                $_SESSION['admin_name'] = $admin['first_name'] . " " . $admin['last_name'];
                
                echo "تم إنشاء الجلسة بنجاح / Session created successfully<br>";
                echo "معرف الجلسة / Session ID: " . session_id() . "<br>";
                echo "معرف المدير / Admin ID: " . $_SESSION['admin_id'] . "<br>";
                echo "</div>";
                
                // رابط للذهاب إلى لوحة التحكم
                echo "<a href='dashboard' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى لوحة التحكم / Go to Dashboard</a><br><br>";
                
            } else {
                echo "<div style='color: red; background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>❌ فشل تسجيل الدخول / Login Failed!</h4>";
                echo "الأسباب / Reasons:<br>";
                if (!$passwordMatch) echo "- كلمة المرور خاطئة / Wrong password<br>";
                if (!$accountActive) echo "- الحساب غير نشط / Account inactive<br>";
                if (!$notDeleted) echo "- الحساب محذوف / Account deleted<br>";
                echo "</div>";
            }
            
        } else {
            echo "<span style='color: red;'>❌ المستخدم غير موجود</span><br>";
            
            // إنشاء مستخدم جديد
            echo "<h4>إنشاء مستخدم جديد / Create New User</h4>";
            $hashedPassword = password_hash($password, PASSWORD_BCRYPT);
            
            $createStmt = $pdo->prepare("INSERT INTO admin (first_name, last_name, email, password, role, mobile1, mobile2, created_on, account_status, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), '1', '0')");
            $createResult = $createStmt->execute([
                'Demo',
                'Admin',
                $email,
                $hashedPassword,
                'Super',
                '***********',
                '***********'
            ]);
            
            if ($createResult) {
                echo "<span style='color: green;'>✅ تم إنشاء المستخدم الجديد بنجاح</span><br>";
                echo "يمكنك الآن تسجيل الدخول بهذه البيانات<br>";
            } else {
                echo "<span style='color: red;'>❌ فشل في إنشاء المستخدم الجديد</span><br>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<span style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</span><br>";
    }
    
    echo "<hr>";
}

// نموذج الاختبار
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار تسجيل الدخول المباشر</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; }
        input[type="email"], input[type="password"] { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 5px; }
        button { background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h3>نموذج اختبار تسجيل الدخول / Login Test Form</h3>
        <form method="POST">
            <label>البريد الإلكتروني / Email:</label>
            <input type="email" name="email" value="<EMAIL>" required>
            
            <label>كلمة المرور / Password:</label>
            <input type="password" name="password" value="********" required>
            
            <button type="submit" name="test_login">اختبار تسجيل الدخول / Test Login</button>
        </form>
        
        <hr>
        <p><strong>ملاحظة:</strong> هذا الملف سيقوم بإصلاح أي مشاكل في كلمة المرور أو حالة الحساب تلقائياً.</p>
        <p><strong>Note:</strong> This file will automatically fix any password or account status issues.</p>
    </div>
</body>
</html>
