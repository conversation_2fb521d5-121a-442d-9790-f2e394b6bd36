<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول / Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .debug {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار تسجيل الدخول / Login Test</h1>
        <hr>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني / Email:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور / Password:</label>
                <input type="password" id="password" name="password" value="12345678" required>
            </div>
            
            <button type="submit">تسجيل الدخول / Login</button>
            <button type="button" onclick="testDirectLogin()">اختبار مباشر / Direct Test</button>
            <button type="button" onclick="checkSession()">فحص الجلسة / Check Session</button>
            <button type="button" onclick="clearResults()">مسح النتائج / Clear Results</button>
        </form>
        
        <div id="results"></div>
    </div>

    <script>
        // تحديد المسار الأساسي للتطبيق
        const appRoot = window.location.origin + '/';
        
        // معالج إرسال النموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            addResult('info', 'بدء اختبار تسجيل الدخول... / Starting login test...');
            
            // اختبار AJAX مثل النظام الأصلي
            testAjaxLogin(email, password);
        });
        
        function testAjaxLogin(email, password) {
            addResult('info', 'إرسال طلب AJAX... / Sending AJAX request...');
            
            const xhr = new XMLHttpRequest();
            xhr.open('POST', appRoot + 'access/login', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    addResult('info', `حالة الاستجابة / Response Status: ${xhr.status} ${xhr.statusText}`);
                    
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            addResult('info', 'استجابة JSON / JSON Response:', JSON.stringify(response, null, 2));
                            
                            if (response.status === 1) {
                                addResult('success', '✅ تم تسجيل الدخول بنجاح! / Login successful!');
                                
                                // اختبار إعادة التوجيه
                                setTimeout(() => {
                                    addResult('info', 'اختبار إعادة التوجيه... / Testing redirect...');
                                    window.location.href = appRoot + 'dashboard';
                                }, 2000);
                                
                            } else {
                                addResult('error', '❌ فشل تسجيل الدخول / Login failed: ' + (response.msg || 'Unknown error'));
                            }
                        } catch (e) {
                            addResult('error', '❌ خطأ في تحليل JSON / JSON parse error: ' + e.message);
                            addResult('debug', 'Raw Response: ' + xhr.responseText);
                        }
                    } else {
                        addResult('error', `❌ خطأ HTTP / HTTP Error: ${xhr.status} ${xhr.statusText}`);
                        addResult('debug', 'Response: ' + xhr.responseText);
                    }
                }
            };
            
            xhr.onerror = function() {
                addResult('error', '❌ خطأ في الشبكة / Network error');
            };
            
            const data = `email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`;
            addResult('debug', 'Request Data: ' + data);
            
            xhr.send(data);
        }
        
        function testDirectLogin() {
            addResult('info', 'اختبار تسجيل الدخول المباشر... / Testing direct login...');
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            fetch('debug_login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `test_login=1&email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`
            })
            .then(response => response.text())
            .then(data => {
                addResult('info', 'نتيجة الاختبار المباشر / Direct test result:');
                addResult('debug', data);
            })
            .catch(error => {
                addResult('error', '❌ خطأ في الاختبار المباشر / Direct test error: ' + error.message);
            });
        }
        
        function checkSession() {
            addResult('info', 'فحص الجلسة... / Checking session...');
            
            fetch(appRoot + 'access/css', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 1) {
                    addResult('success', '✅ الجلسة نشطة / Session is active');
                } else {
                    addResult('error', '❌ الجلسة غير نشطة / Session is not active');
                }
                addResult('debug', 'Session Response: ' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                addResult('error', '❌ خطأ في فحص الجلسة / Session check error: ' + error.message);
            });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function addResult(type, title, content = '') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            
            let html = `<strong>${title}</strong>`;
            if (content) {
                html += `<div class="debug">${content}</div>`;
            }
            
            resultDiv.innerHTML = html;
            resultsDiv.appendChild(resultDiv);
            
            // التمرير إلى النتيجة الجديدة
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addResult('info', 'تم تحميل الصفحة / Page loaded');
            addResult('info', 'المسار الأساسي / App Root: ' + appRoot);
            
            // فحص الجلسة الحالية
            checkSession();
        });
    </script>
</body>
</html>
