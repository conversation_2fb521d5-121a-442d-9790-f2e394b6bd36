<?php
/**
 * ملف تحديث كلمة مرور المدير
 * Admin Password Update Script
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'kidzrcle_MBEAAAT';
$username = 'kidzrcle_MBEAAAT';
$password = 'kidzrcle_MBEAAAT';

echo "<h2>تحديث كلمة مرور المدير / Admin Password Update</h2>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</div><br>";
    
    // كلمة المرور الجديدة
    $newPassword = '12345678';
    $adminEmail = '<EMAIL>';
    
    // تشفير كلمة المرور باستخدام bcrypt
    $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);
    
    echo "<strong>معلومات التحديث:</strong><br>";
    echo "البريد الإلكتروني / Email: $adminEmail<br>";
    echo "كلمة المرور الجديدة / New Password: $newPassword<br>";
    echo "كلمة المرور المشفرة / Hashed Password: $hashedPassword<br><br>";
    
    // تحديث كلمة المرور في قاعدة البيانات
    $stmt = $pdo->prepare("UPDATE admin SET password = ? WHERE email = ?");
    $result = $stmt->execute([$hashedPassword, $adminEmail]);
    
    if ($result && $stmt->rowCount() > 0) {
        echo "<div style='color: green; font-weight: bold; padding: 10px; border: 2px solid green; background-color: #f0fff0;'>";
        echo "✅ تم تحديث كلمة المرور بنجاح!<br>";
        echo "✅ Password updated successfully!<br><br>";
        echo "<strong>بيانات الدخول الجديدة / New Login Credentials:</strong><br>";
        echo "البريد الإلكتروني / Email: <strong>$adminEmail</strong><br>";
        echo "كلمة المرور / Password: <strong>$newPassword</strong><br>";
        echo "</div>";
        
        // التحقق من صحة كلمة المرور المحدثة
        echo "<br><h3>اختبار كلمة المرور / Password Test:</h3>";
        $stmt = $pdo->prepare("SELECT password FROM admin WHERE email = ?");
        $stmt->execute([$adminEmail]);
        $storedHash = $stmt->fetchColumn();
        
        if (password_verify($newPassword, $storedHash)) {
            echo "<div style='color: green;'>✅ اختبار كلمة المرور نجح! / Password verification successful!</div>";
        } else {
            echo "<div style='color: red;'>❌ فشل اختبار كلمة المرور! / Password verification failed!</div>";
        }
        
    } else {
        echo "<div style='color: red; font-weight: bold;'>";
        echo "❌ فشل في تحديث كلمة المرور!<br>";
        echo "❌ Failed to update password!<br>";
        echo "تأكد من وجود المستخدم في قاعدة البيانات / Make sure the user exists in the database<br>";
        echo "</div>";
    }
    
    // عرض معلومات المدير الحالية
    echo "<br><h3>معلومات المدير الحالية / Current Admin Info:</h3>";
    $stmt = $pdo->prepare("SELECT id, first_name, last_name, email, role, account_status, deleted FROM admin WHERE email = ?");
    $stmt->execute([$adminEmail]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الحقل / Field</th><th>القيمة / Value</th></tr>";
        foreach ($admin as $key => $value) {
            echo "<tr><td>$key</td><td>$value</td></tr>";
        }
        echo "</table>";
        
        // التحقق من حالة الحساب
        if ($admin['account_status'] != '1') {
            echo "<div style='color: orange; font-weight: bold; margin-top: 10px;'>";
            echo "⚠️ تحذير: حالة الحساب غير نشطة! / Warning: Account status is inactive!<br>";
            echo "قم بتفعيل الحساب لتتمكن من تسجيل الدخول / Activate the account to be able to login<br>";
            echo "</div>";
        }
        
        if ($admin['deleted'] == '1') {
            echo "<div style='color: red; font-weight: bold; margin-top: 10px;'>";
            echo "❌ تحذير: الحساب محذوف! / Warning: Account is deleted!<br>";
            echo "قم بإلغاء حذف الحساب لتتمكن من تسجيل الدخول / Undelete the account to be able to login<br>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='color: red;'>❌ لم يتم العثور على المدير! / Admin not found!</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>";
    echo "❌ خطأ في قاعدة البيانات! / Database Error!<br>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "</div>";
}

echo "<hr>";
echo "<div style='background-color: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin-top: 20px;'>";
echo "<strong>ملاحظات مهمة / Important Notes:</strong><br>";
echo "1. احذف هذا الملف بعد تحديث كلمة المرور لأسباب أمنية<br>";
echo "1. Delete this file after updating the password for security reasons<br>";
echo "2. تأكد من أن حالة الحساب نشطة (account_status = 1)<br>";
echo "2. Make sure the account status is active (account_status = 1)<br>";
echo "3. تأكد من أن الحساب غير محذوف (deleted = 0)<br>";
echo "3. Make sure the account is not deleted (deleted = 0)<br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
table {
    background-color: white;
    margin: 10px 0;
}
th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
</style>
