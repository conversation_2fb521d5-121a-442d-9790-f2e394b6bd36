<?php
/**
 * ملف فحص إعدادات URL
 * URL Settings Check File
 */

// تضمين ملف التهيئة
require_once 'application/config/config.php';

echo "<h2>فحص إعدادات URL / URL Settings Check</h2>";
echo "<hr>";

// عرض المعلومات الأساسية
echo "<h3>معلومات الخادم / Server Info</h3>";
echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "<br>";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "PHP_SELF: " . $_SERVER['PHP_SELF'] . "<br>";
echo "DOCUMENT_ROOT: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<br>";

// عرض إعدادات URL الأساسي
echo "<h3>إعدادات URL الأساسي / Base URL Settings</h3>";
echo "base_url: " . $config['base_url'] . "<br>";
echo "<br>";

// اختبار المسارات
echo "<h3>اختبار المسارات / Routes Test</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>المسار / Route</th><th>URL الكامل / Full URL</th></tr>";

$routes = [
    'access/login',
    'access/css',
    'dashboard',
    'items',
    'transactions',
    'administrators'
];

foreach ($routes as $route) {
    echo "<tr>";
    echo "<td>$route</td>";
    echo "<td><a href='" . $config['base_url'] . $route . "' target='_blank'>" . $config['base_url'] . $route . "</a></td>";
    echo "</tr>";
}

echo "</table>";
echo "<br>";

// اختبار ملف .htaccess
echo "<h3>اختبار ملف .htaccess / .htaccess Test</h3>";
echo "RewriteEngine: " . (in_array('mod_rewrite', apache_get_modules()) ? "مفعل / Enabled" : "غير مفعل / Disabled") . "<br>";
echo "<br>";

// اختبار الجلسات
echo "<h3>اختبار الجلسات / Session Test</h3>";
session_start();
$_SESSION['test'] = 'test_' . time();
echo "Session ID: " . session_id() . "<br>";
echo "Session Test Value: " . $_SESSION['test'] . "<br>";
echo "<br>";

// اختبار قاعدة البيانات
echo "<h3>اختبار قاعدة البيانات / Database Test</h3>";
try {
    $host = 'localhost';
    $dbname = 'kidzrcle_MBEAAAT';
    $username = 'kidzrcle_MBEAAAT';
    $password = 'kidzrcle_MBEAAAT';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<span style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح / Database connection successful</span><br>";
} catch (PDOException $e) {
    echo "<span style='color: red;'>❌ خطأ في قاعدة البيانات / Database error: " . $e->getMessage() . "</span><br>";
}

echo "<hr>";
echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin-top: 20px;'>";
echo "<strong>ملخص / Summary:</strong><br>";
echo "1. تأكد من أن base_url ينتهي بـ / <br>";
echo "2. تأكد من أن RewriteBase في ملف .htaccess صحيح<br>";
echo "3. تأكد من أن جميع المسارات تعمل بشكل صحيح<br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
table {
    background-color: white;
    margin: 10px 0;
}
th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
</style>
