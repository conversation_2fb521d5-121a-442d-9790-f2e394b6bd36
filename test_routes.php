<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المسارات / Routes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .debug {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار المسارات / Routes Test</h1>
        <hr>
        
        <div>
            <button onclick="testRoute('access/login', 'POST', {email: '<EMAIL>', password: '12345678'})">
                اختبار تسجيل الدخول / Test Login
            </button>
            
            <button onclick="testRoute('access/css', 'GET')">
                اختبار فحص الجلسة / Test Session Check
            </button>
            
            <button onclick="testRoute('dashboard', 'GET')">
                اختبار لوحة التحكم / Test Dashboard
            </button>
            
            <button onclick="testRoute('items', 'GET')">
                اختبار المنتجات / Test Items
            </button>
            
            <button onclick="testRoute('', 'GET')">
                اختبار الصفحة الرئيسية / Test Home
            </button>
            
            <button onclick="clearResults()">
                مسح النتائج / Clear Results
            </button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        // تحديد المسار الأساسي
        const baseUrl = window.location.origin + '/';
        
        function testRoute(route, method = 'GET', data = null) {
            const url = baseUrl + route;
            addResult('info', `اختبار المسار / Testing route: ${method} ${url}`);
            
            const options = {
                method: method,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            if (method === 'POST' && data) {
                options.headers['Content-Type'] = 'application/x-www-form-urlencoded';
                options.body = new URLSearchParams(data).toString();
                addResult('debug', 'POST Data: ' + options.body);
            }
            
            fetch(url, options)
                .then(response => {
                    addResult('info', `حالة الاستجابة / Response Status: ${response.status} ${response.statusText}`);
                    
                    if (response.ok) {
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(data => {
                    try {
                        // محاولة تحليل JSON
                        const jsonData = JSON.parse(data);
                        addResult('success', `✅ استجابة JSON صحيحة / Valid JSON response`);
                        addResult('debug', JSON.stringify(jsonData, null, 2));
                        
                        // فحص نتيجة تسجيل الدخول
                        if (route === 'access/login' && jsonData.status === 1) {
                            addResult('success', '🎉 تم تسجيل الدخول بنجاح! / Login successful!');
                        } else if (route === 'access/login' && jsonData.status === 0) {
                            addResult('error', '❌ فشل تسجيل الدخول / Login failed: ' + (jsonData.msg || 'Unknown error'));
                        }
                        
                    } catch (e) {
                        // ليس JSON، عرض النص
                        if (data.includes('<!DOCTYPE html>') || data.includes('<html>')) {
                            addResult('info', '📄 استجابة HTML / HTML response');
                            
                            // فحص إذا كانت صفحة خطأ
                            if (data.includes('404') || data.includes('Not Found')) {
                                addResult('error', '❌ صفحة غير موجودة / Page not found');
                            } else if (data.includes('500') || data.includes('Internal Server Error')) {
                                addResult('error', '❌ خطأ في الخادم / Server error');
                            } else {
                                addResult('success', '✅ تم تحميل الصفحة بنجاح / Page loaded successfully');
                            }
                        } else {
                            addResult('info', '📝 استجابة نصية / Text response');
                        }
                        
                        // عرض جزء من المحتوى
                        const preview = data.substring(0, 500) + (data.length > 500 ? '...' : '');
                        addResult('debug', 'Response Preview:\n' + preview);
                    }
                })
                .catch(error => {
                    addResult('error', `❌ خطأ في الطلب / Request error: ${error.message}`);
                });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function addResult(type, title, content = '') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            
            let html = `<strong>${title}</strong>`;
            if (content) {
                html += `<div class="debug">${content}</div>`;
            }
            
            resultDiv.innerHTML = html;
            resultsDiv.appendChild(resultDiv);
            
            // التمرير إلى النتيجة الجديدة
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // عرض معلومات أساسية عند تحميل الصفحة
        window.addEventListener('load', function() {
            addResult('info', 'تم تحميل الصفحة / Page loaded');
            addResult('info', 'المسار الأساسي / Base URL: ' + baseUrl);
            addResult('info', 'المضيف / Host: ' + window.location.host);
            addResult('info', 'البروتوكول / Protocol: ' + window.location.protocol);
        });
    </script>
</body>
</html>
