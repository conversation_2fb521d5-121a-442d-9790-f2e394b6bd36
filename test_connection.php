<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * Database Connection Test File
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'kidzrcle_MBEAAAT';
$username = 'kidzrcle_MBEAAAT';
$password = 'kidzrcle_MBEAAAT';

echo "<h2>اختبار الاتصال بقاعدة البيانات / Database Connection Test</h2>";
echo "<hr>";

try {
    // محاولة الاتصال باستخدام PDO
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='color: green; font-weight: bold;'>";
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح!<br>";
    echo "✅ Database connection successful!<br>";
    echo "</div>";
    
    // اختبار الجداول
    echo "<h3>الجداول الموجودة / Available Tables:</h3>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        // اختبار جدول المديرين
        echo "<h3>اختبار بيانات المديرين / Admin Data Test:</h3>";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "عدد المديرين في النظام / Number of admins: " . $result['count'] . "<br>";
        
        // اختبار جدول المنتجات
        echo "<h3>اختبار بيانات المنتجات / Items Data Test:</h3>";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM items");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "عدد المنتجات في النظام / Number of items: " . $result['count'] . "<br>";
        
    } else {
        echo "<div style='color: orange;'>";
        echo "⚠️ قاعدة البيانات فارغة. يرجى استيراد ملف 1410inventory.sql<br>";
        echo "⚠️ Database is empty. Please import 1410inventory.sql file<br>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>";
    echo "❌ فشل في الاتصال بقاعدة البيانات!<br>";
    echo "❌ Database connection failed!<br>";
    echo "خطأ / Error: " . $e->getMessage() . "<br>";
    echo "</div>";
    
    echo "<h3>خطوات استكشاف الأخطاء / Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li>تأكد من أن MySQL يعمل / Ensure MySQL is running</li>";
    echo "<li>تأكد من صحة بيانات الاتصال / Verify connection credentials</li>";
    echo "<li>تأكد من وجود قاعدة البيانات / Ensure database exists</li>";
    echo "<li>تأكد من صلاحيات المستخدم / Check user privileges</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<h3>معلومات الإعداد / Configuration Info:</h3>";
echo "الخادم / Host: $host<br>";
echo "قاعدة البيانات / Database: $dbname<br>";
echo "المستخدم / Username: $username<br>";
echo "كلمة المرور / Password: " . str_repeat('*', strlen($password)) . "<br>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> احذف هذا الملف بعد التأكد من نجاح الاتصال لأسباب أمنية.</p>";
echo "<p><strong>Note:</strong> Delete this file after confirming successful connection for security reasons.</p>";
?>
