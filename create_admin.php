<?php
/**
 * ملف إنشاء مدير جديد
 * Create New Admin Script
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'kidzrcle_MBEAAAT';
$username = 'kidzrcle_MBEAAAT';
$password = 'kidzrcle_MBEAAAT';

echo "<h2>إنشاء مدير جديد / Create New Admin</h2>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</div><br>";
    
    // بيانات المدير الجديد
    $adminData = [
        'first_name' => 'Admin',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'password' => '12345678',
        'role' => 'Super',
        'mobile1' => '01234567890',
        'mobile2' => '01987654321'
    ];
    
    // تشفير كلمة المرور
    $hashedPassword = password_hash($adminData['password'], PASSWORD_BCRYPT);
    
    echo "<strong>بيانات المدير الجديد / New Admin Data:</strong><br>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background-color: white;'>";
    echo "<tr><th>الحقل / Field</th><th>القيمة / Value</th></tr>";
    echo "<tr><td>الاسم الأول / First Name</td><td>{$adminData['first_name']}</td></tr>";
    echo "<tr><td>الاسم الأخير / Last Name</td><td>{$adminData['last_name']}</td></tr>";
    echo "<tr><td>البريد الإلكتروني / Email</td><td>{$adminData['email']}</td></tr>";
    echo "<tr><td>كلمة المرور / Password</td><td>{$adminData['password']}</td></tr>";
    echo "<tr><td>الدور / Role</td><td>{$adminData['role']}</td></tr>";
    echo "<tr><td>الهاتف الأول / Mobile 1</td><td>{$adminData['mobile1']}</td></tr>";
    echo "<tr><td>الهاتف الثاني / Mobile 2</td><td>{$adminData['mobile2']}</td></tr>";
    echo "</table><br>";
    
    // التحقق من وجود البريد الإلكتروني
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin WHERE email = ?");
    $stmt->execute([$adminData['email']]);
    $emailExists = $stmt->fetchColumn();
    
    if ($emailExists > 0) {
        echo "<div style='color: orange; font-weight: bold;'>";
        echo "⚠️ البريد الإلكتروني موجود مسبقاً! / Email already exists!<br>";
        echo "سيتم تحديث كلمة المرور للمستخدم الموجود / Will update password for existing user<br>";
        echo "</div><br>";
        
        // تحديث كلمة المرور للمستخدم الموجود
        $stmt = $pdo->prepare("UPDATE admin SET password = ?, first_name = ?, last_name = ?, role = ?, mobile1 = ?, mobile2 = ? WHERE email = ?");
        $result = $stmt->execute([
            $hashedPassword,
            $adminData['first_name'],
            $adminData['last_name'],
            $adminData['role'],
            $adminData['mobile1'],
            $adminData['mobile2'],
            $adminData['email']
        ]);
        
        if ($result) {
            echo "<div style='color: green; font-weight: bold;'>✅ تم تحديث بيانات المدير بنجاح! / Admin data updated successfully!</div>";
        }
        
    } else {
        // إنشاء مدير جديد
        $stmt = $pdo->prepare("INSERT INTO admin (first_name, last_name, email, password, role, mobile1, mobile2, created_on, account_status, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), '1', '0')");
        $result = $stmt->execute([
            $adminData['first_name'],
            $adminData['last_name'],
            $adminData['email'],
            $hashedPassword,
            $adminData['role'],
            $adminData['mobile1'],
            $adminData['mobile2']
        ]);
        
        if ($result) {
            $newAdminId = $pdo->lastInsertId();
            echo "<div style='color: green; font-weight: bold;'>";
            echo "✅ تم إنشاء المدير الجديد بنجاح! / New admin created successfully!<br>";
            echo "معرف المدير الجديد / New Admin ID: $newAdminId<br>";
            echo "</div>";
        }
    }
    
    // اختبار كلمة المرور
    echo "<br><h3>اختبار كلمة المرور / Password Test:</h3>";
    $stmt = $pdo->prepare("SELECT password FROM admin WHERE email = ?");
    $stmt->execute([$adminData['email']]);
    $storedHash = $stmt->fetchColumn();
    
    if (password_verify($adminData['password'], $storedHash)) {
        echo "<div style='color: green;'>✅ اختبار كلمة المرور نجح! / Password verification successful!</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل اختبار كلمة المرور! / Password verification failed!</div>";
    }
    
    // عرض جميع المديرين
    echo "<br><h3>جميع المديرين في النظام / All Admins in System:</h3>";
    $stmt = $pdo->query("SELECT id, first_name, last_name, email, role, account_status, deleted, created_on FROM admin ORDER BY id");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($admins) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background-color: white;'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>الاسم / Name</th>";
        echo "<th>البريد الإلكتروني / Email</th>";
        echo "<th>الدور / Role</th>";
        echo "<th>الحالة / Status</th>";
        echo "<th>محذوف / Deleted</th>";
        echo "<th>تاريخ الإنشاء / Created</th>";
        echo "</tr>";
        
        foreach ($admins as $admin) {
            $statusColor = $admin['account_status'] == '1' ? 'green' : 'red';
            $deletedColor = $admin['deleted'] == '0' ? 'green' : 'red';
            $statusText = $admin['account_status'] == '1' ? 'نشط/Active' : 'غير نشط/Inactive';
            $deletedText = $admin['deleted'] == '0' ? 'لا/No' : 'نعم/Yes';
            
            echo "<tr>";
            echo "<td>{$admin['id']}</td>";
            echo "<td>{$admin['first_name']} {$admin['last_name']}</td>";
            echo "<td>{$admin['email']}</td>";
            echo "<td>{$admin['role']}</td>";
            echo "<td style='color: $statusColor;'>$statusText</td>";
            echo "<td style='color: $deletedColor;'>$deletedText</td>";
            echo "<td>{$admin['created_on']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>";
    echo "❌ خطأ في قاعدة البيانات! / Database Error!<br>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "</div>";
}

echo "<hr>";
echo "<div style='background-color: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin-top: 20px;'>";
echo "<strong>ملاحظات مهمة / Important Notes:</strong><br>";
echo "1. احذف هذا الملف بعد إنشاء المدير لأسباب أمنية<br>";
echo "1. Delete this file after creating the admin for security reasons<br>";
echo "2. يمكنك تعديل بيانات المدير في أعلى الملف قبل التشغيل<br>";
echo "2. You can modify admin data at the top of the file before running<br>";
echo "3. تأكد من أن البريد الإلكتروني فريد<br>";
echo "3. Make sure the email is unique<br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
table {
    background-color: white;
    margin: 10px 0;
}
th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
</style>
