<?php
/**
 * ملف تشخيص مشاكل تسجيل الدخول الشامل
 * Comprehensive Login Debug Script
 */

// بدء الجلسة
session_start();

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'kidzrcle_MBEAAAT';
$username = 'kidzrcle_MBEAAAT';
$password = 'kidzrcle_MBEAAAT';

echo "<h2>تشخيص مشاكل تسجيل الدخول / Login Debug</h2>";
echo "<hr>";

// 1. فحص إعدادات PHP
echo "<h3>1. إعدادات PHP / PHP Settings</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; background-color: white;'>";
echo "<tr><th>الإعداد / Setting</th><th>القيمة / Value</th><th>الحالة / Status</th></tr>";

$phpSettings = [
    'session.auto_start' => ini_get('session.auto_start'),
    'session.use_cookies' => ini_get('session.use_cookies'),
    'session.use_only_cookies' => ini_get('session.use_only_cookies'),
    'session.cookie_httponly' => ini_get('session.cookie_httponly'),
    'session.cookie_secure' => ini_get('session.cookie_secure'),
    'session.save_path' => ini_get('session.save_path'),
    'session.gc_maxlifetime' => ini_get('session.gc_maxlifetime'),
    'allow_url_fopen' => ini_get('allow_url_fopen'),
    'display_errors' => ini_get('display_errors')
];

foreach ($phpSettings as $setting => $value) {
    $status = '';
    if ($setting == 'session.auto_start' && $value == '1') {
        $status = '<span style="color: red;">⚠️ يجب أن يكون 0</span>';
    } elseif ($setting == 'session.use_cookies' && $value != '1') {
        $status = '<span style="color: red;">⚠️ يجب أن يكون 1</span>';
    } else {
        $status = '<span style="color: green;">✅</span>';
    }
    
    echo "<tr><td>$setting</td><td>$value</td><td>$status</td></tr>";
}
echo "</table><br>";

// 2. فحص مجلد الجلسات
echo "<h3>2. فحص مجلد الجلسات / Session Directory Check</h3>";
$sessionPath = dirname(__FILE__) . '/system/sessions';
echo "مسار الجلسات / Session Path: <strong>$sessionPath</strong><br>";

if (is_dir($sessionPath)) {
    echo "<span style='color: green;'>✅ المجلد موجود / Directory exists</span><br>";
    
    if (is_writable($sessionPath)) {
        echo "<span style='color: green;'>✅ المجلد قابل للكتابة / Directory is writable</span><br>";
    } else {
        echo "<span style='color: red;'>❌ المجلد غير قابل للكتابة / Directory is not writable</span><br>";
        echo "<strong>الحل / Solution:</strong> chmod 755 $sessionPath<br>";
    }
    
    $files = scandir($sessionPath);
    $sessionFiles = array_filter($files, function($file) {
        return strpos($file, 'sess_') === 0;
    });
    
    echo "عدد ملفات الجلسات / Session files count: " . count($sessionFiles) . "<br>";
} else {
    echo "<span style='color: red;'>❌ المجلد غير موجود / Directory does not exist</span><br>";
    echo "<strong>الحل / Solution:</strong> mkdir -p $sessionPath && chmod 755 $sessionPath<br>";
}

echo "<br>";

// 3. اختبار قاعدة البيانات
echo "<h3>3. اختبار قاعدة البيانات / Database Test</h3>";

try {
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<span style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح / Database connection successful</span><br>";
    
    // فحص جدول المديرين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin WHERE account_status = '1' AND deleted = '0'");
    $activeAdmins = $stmt->fetchColumn();
    echo "عدد المديرين النشطين / Active admins: <strong>$activeAdmins</strong><br>";
    
    if ($activeAdmins == 0) {
        echo "<span style='color: red;'>❌ لا يوجد مديرين نشطين / No active admins found</span><br>";
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>❌ خطأ في قاعدة البيانات / Database error: " . $e->getMessage() . "</span><br>";
}

echo "<br>";

// 4. محاكاة تسجيل الدخول
echo "<h3>4. محاكاة تسجيل الدخول / Login Simulation</h3>";

if (isset($_POST['test_login'])) {
    $testEmail = $_POST['email'];
    $testPassword = $_POST['password'];
    
    echo "<strong>اختبار تسجيل الدخول / Testing login for:</strong> $testEmail<br>";
    
    try {
        // جلب بيانات المدير
        $stmt = $pdo->prepare("SELECT id, first_name, last_name, email, password, role, account_status, deleted FROM admin WHERE email = ?");
        $stmt->execute([$testEmail]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<span style='color: green;'>✅ المستخدم موجود / User found</span><br>";
            
            // فحص كلمة المرور
            $passwordMatch = password_verify($testPassword, $admin['password']);
            echo "كلمة المرور صحيحة / Password correct: " . ($passwordMatch ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . "<br>";
            
            // فحص حالة الحساب
            $accountActive = $admin['account_status'] == '1';
            echo "الحساب نشط / Account active: " . ($accountActive ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . "<br>";
            
            // فحص حالة الحذف
            $notDeleted = $admin['deleted'] == '0';
            echo "الحساب غير محذوف / Not deleted: " . ($notDeleted ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . "<br>";
            
            if ($passwordMatch && $accountActive && $notDeleted) {
                // محاولة إنشاء جلسة
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_email'] = $admin['email'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['admin_name'] = $admin['first_name'] . ' ' . $admin['last_name'];
                
                echo "<div style='color: green; font-weight: bold; padding: 10px; border: 2px solid green; background-color: #f0fff0;'>";
                echo "✅ تم تسجيل الدخول بنجاح! / Login successful!<br>";
                echo "معرف الجلسة / Session ID: " . session_id() . "<br>";
                echo "معرف المدير / Admin ID: " . $_SESSION['admin_id'] . "<br>";
                echo "</div>";
                
                // اختبار إعادة التوجيه
                echo "<br><strong>اختبار إعادة التوجيه / Redirect Test:</strong><br>";
                echo "<a href='index.php' style='background-color: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>اذهب إلى الصفحة الرئيسية / Go to Main Page</a><br>";
                
            } else {
                echo "<div style='color: red; font-weight: bold;'>❌ فشل تسجيل الدخول / Login failed</div>";
                
                if (!$passwordMatch) {
                    echo "- كلمة المرور خاطئة / Wrong password<br>";
                }
                if (!$accountActive) {
                    echo "- الحساب غير نشط / Account inactive<br>";
                }
                if (!$notDeleted) {
                    echo "- الحساب محذوف / Account deleted<br>";
                }
            }
            
        } else {
            echo "<span style='color: red;'>❌ المستخدم غير موجود / User not found</span><br>";
        }
        
    } catch (PDOException $e) {
        echo "<span style='color: red;'>❌ خطأ في قاعدة البيانات / Database error: " . $e->getMessage() . "</span><br>";
    }
    
    echo "<br>";
}

// 5. نموذج اختبار تسجيل الدخول
echo "<h3>5. اختبار تسجيل الدخول / Login Test Form</h3>";
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px;'>";
echo "<table>";
echo "<tr><td>البريد الإلكتروني / Email:</td><td><input type='email' name='email' value='<EMAIL>' required style='width: 200px; padding: 5px;'></td></tr>";
echo "<tr><td>كلمة المرور / Password:</td><td><input type='password' name='password' value='********' required style='width: 200px; padding: 5px;'></td></tr>";
echo "<tr><td colspan='2'><input type='submit' name='test_login' value='اختبار تسجيل الدخول / Test Login' style='background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'></td></tr>";
echo "</table>";
echo "</form><br>";

// 6. فحص الجلسة الحالية
echo "<h3>6. الجلسة الحالية / Current Session</h3>";
echo "معرف الجلسة / Session ID: <strong>" . session_id() . "</strong><br>";
echo "حالة الجلسة / Session Status: <strong>" . (session_status() == PHP_SESSION_ACTIVE ? 'نشطة / Active' : 'غير نشطة / Inactive') . "</strong><br>";

if (!empty($_SESSION)) {
    echo "<strong>بيانات الجلسة / Session Data:</strong><br>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    print_r($_SESSION);
    echo "</pre>";
} else {
    echo "<span style='color: orange;'>⚠️ لا توجد بيانات في الجلسة / No session data</span><br>";
}

// 7. اختبار AJAX
echo "<h3>7. اختبار AJAX / AJAX Test</h3>";
echo "<div id='ajax-test-result'></div>";
echo "<button onclick='testAjaxLogin()' style='background-color: #17a2b8; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>اختبار AJAX / Test AJAX</button><br>";

echo "<script>
function testAjaxLogin() {
    var result = document.getElementById('ajax-test-result');
    result.innerHTML = 'جاري الاختبار... / Testing...';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'access/login', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    result.innerHTML = '<span style=\"color: green;\">✅ AJAX يعمل بشكل صحيح / AJAX working correctly</span><br>Response: ' + JSON.stringify(response);
                } catch (e) {
                    result.innerHTML = '<span style=\"color: red;\">❌ خطأ في تحليل JSON / JSON parse error</span><br>Response: ' + xhr.responseText;
                }
            } else {
                result.innerHTML = '<span style=\"color: red;\">❌ خطأ HTTP / HTTP Error: ' + xhr.status + ' ' + xhr.statusText + '</span>';
            }
        }
    };
    
    xhr.send('email=<EMAIL>&password=********');
}
</script>";

echo "<hr>";
echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin-top: 20px;'>";
echo "<strong>الخطوات التالية / Next Steps:</strong><br>";
echo "1. تأكد من أن جميع الفحوصات أعلاه تظهر ✅<br>";
echo "2. إذا كان هناك ❌، اتبع الحلول المقترحة<br>";
echo "3. جرب اختبار تسجيل الدخول أعلاه<br>";
echo "4. إذا نجح الاختبار هنا ولكن فشل في الموقع، فالمشكلة في JavaScript<br>";
echo "5. احذف هذا الملف بعد حل المشكلة<br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
table {
    background-color: white;
    margin: 10px 0;
}
th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
</style>
