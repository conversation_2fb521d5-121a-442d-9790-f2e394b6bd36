# Mini-Inventory-and-Sales-Management-System
An Inventory and Sales Management System written in PHP (codeIgniter) with support for MySQL and Sqlite3 databases


# Features
- Products Management
- Transaction Management
- Viewing and Printing of Transactions Within Specific Dates
- Barcode Scanning of Products for product and transaction management
- Custom Analytics
- User Management
- Monthly/Yearly Earnings Overview
- VAT
- Discount

# Getting Started
- _mod_rewrite_ must be enabled
- PHP >= 5.4 is recommended
- Browser with support for most ES6 features is recommended
- If you edit the folder name, you will need to do the following:
  - Open __.htaccess in the root folder__ and change the line with __"mini-inventory-and-sales-management-system"__ to your new folder name. Leave it blank if app is directly inside your __www__ or __html__ directory.
  - Open __"application/config/config.php"__ and do the same.
  - Open __"public/js/main.js"__ and do the same.

# Demo
View demo here: https://1410inc.xyz/mini-inventory-and-sales-management-system/

To log in:

- Email: <EMAIL>
- Password: demopass



# New Version
A new version is under development and can be checked at https://inventory.1410inc.xyz/

You can create an account for testing purpose.
