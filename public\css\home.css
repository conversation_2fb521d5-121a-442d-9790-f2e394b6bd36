/* تنسيقات الصفحة الرئيسية / Home Page Styles */

.welcome-section {
    background-color: rgba(255,255,255,0.95);
    padding: 40px;
    border-radius: 15px;
    margin-top: 50px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
}

.welcome-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.welcome-section h3 {
    color: #34495e;
    margin-bottom: 30px;
    font-weight: 300;
}

.welcome-section p {
    color: #555;
    font-size: 16px;
    line-height: 1.8;
    margin-bottom: 15px;
}

.btn-custom {
    margin: 8px;
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    min-width: 180px;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    text-decoration: none;
}

.btn-login {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    border: none;
    color: white;
}

.btn-login:hover {
    background: linear-gradient(45deg, #e67e22, #d35400);
    color: white;
}

.btn-dashboard {
    background: linear-gradient(45deg, #3498db, #2980b9);
    border: none;
    color: white;
}

.btn-dashboard:hover {
    background: linear-gradient(45deg, #2980b9, #1f4e79);
    color: white;
}

.btn-products {
    background: linear-gradient(45deg, #27ae60, #229954);
    border: none;
    color: white;
}

.btn-products:hover {
    background: linear-gradient(45deg, #229954, #1e8449);
    color: white;
}

.btn-transactions {
    background: linear-gradient(45deg, #17a2b8, #138496);
    border: none;
    color: white;
}

.btn-transactions:hover {
    background: linear-gradient(45deg, #138496, #0f6674);
    color: white;
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(50px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .welcome-section {
        padding: 20px;
        margin-top: 20px;
    }
    
    .welcome-section h2 {
        font-size: 24px;
    }
    
    .welcome-section h3 {
        font-size: 18px;
    }
    
    .btn-custom {
        display: block;
        width: 100%;
        margin: 10px 0;
        min-width: auto;
    }
}

/* تحسين الخطوط العربية */
.arabic-text {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

.english-text {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
    text-align: left;
}

/* تأثير الأيقونات */
.btn-custom i {
    margin-right: 8px;
    font-size: 18px;
    transition: transform 0.3s ease;
}

.btn-custom:hover i {
    transform: scale(1.2);
}

/* خلفية متدرجة للصفحة */
.top-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* تحسين شفافية الخلفية */
.inner-bg {
    background: rgba(0,0,0,0.1);
    min-height: 100vh;
    padding: 50px 0;
}
