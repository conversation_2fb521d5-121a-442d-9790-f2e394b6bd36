<?php
/**
 * ملف إصلاح مشاكل تسجيل الدخول
 * Login Issues Fix Script
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'kidzrcle_MBEAAAT';
$username = 'kidzrcle_MBEAAAT';
$password = 'kidzrcle_MBEAAAT';

echo "<h2>إصلاح مشاكل تسجيل الدخول / Login Issues Fix</h2>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</div><br>";
    
    // 1. إصلاح حساب <EMAIL>
    echo "<h3>1. إصلاح حساب المدير الافتراضي / Fix Default Admin Account</h3>";
    
    $demoEmail = '<EMAIL>';
    $newPassword = '********';
    $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);
    
    // تحديث كلمة المرور وتفعيل الحساب
    $stmt = $pdo->prepare("UPDATE admin SET password = ?, account_status = '1', deleted = '0' WHERE email = ?");
    $result = $stmt->execute([$hashedPassword, $demoEmail]);
    
    if ($result && $stmt->rowCount() > 0) {
        echo "<div style='color: green;'>✅ تم إصلاح حساب $demoEmail</div>";
        echo "كلمة المرور الجديدة / New Password: <strong>$newPassword</strong><br><br>";
    } else {
        echo "<div style='color: orange;'>⚠️ لم يتم العثور على حساب $demoEmail أو لا يحتاج إصلاح</div><br>";
    }
    
    // 2. التحقق من جميع الحسابات وإصلاحها
    echo "<h3>2. فحص وإصلاح جميع الحسابات / Check and Fix All Accounts</h3>";
    
    $stmt = $pdo->query("SELECT id, first_name, last_name, email, account_status, deleted FROM admin");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $fixedAccounts = 0;
    
    foreach ($admins as $admin) {
        $needsFix = false;
        $updates = [];
        $updateValues = [];
        
        // إصلاح الحسابات غير النشطة
        if ($admin['account_status'] != '1') {
            $updates[] = "account_status = ?";
            $updateValues[] = '1';
            $needsFix = true;
        }
        
        // إصلاح الحسابات المحذوفة
        if ($admin['deleted'] != '0') {
            $updates[] = "deleted = ?";
            $updateValues[] = '0';
            $needsFix = true;
        }
        
        if ($needsFix) {
            $updateValues[] = $admin['id'];
            $sql = "UPDATE admin SET " . implode(', ', $updates) . " WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($updateValues);
            
            echo "<div style='color: blue;'>🔧 تم إصلاح حساب: {$admin['first_name']} {$admin['last_name']} ({$admin['email']})</div>";
            $fixedAccounts++;
        }
    }
    
    if ($fixedAccounts == 0) {
        echo "<div style='color: green;'>✅ جميع الحسابات سليمة / All accounts are fine</div>";
    } else {
        echo "<div style='color: green;'>✅ تم إصلاح $fixedAccounts حساب / Fixed $fixedAccounts accounts</div>";
    }
    
    echo "<br>";
    
    // 3. اختبار تسجيل الدخول
    echo "<h3>3. اختبار تسجيل الدخول / Login Test</h3>";
    
    $testCredentials = [
        ['email' => '<EMAIL>', 'password' => '********'],
        ['email' => '<EMAIL>', 'password' => 'demo']
    ];
    
    foreach ($testCredentials as $cred) {
        echo "<strong>اختبار / Testing:</strong> {$cred['email']} / {$cred['password']}<br>";
        
        $stmt = $pdo->prepare("SELECT password, account_status, deleted FROM admin WHERE email = ?");
        $stmt->execute([$cred['email']]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            $passwordMatch = password_verify($cred['password'], $admin['password']);
            $accountActive = $admin['account_status'] == '1';
            $notDeleted = $admin['deleted'] == '0';
            
            echo "- كلمة المرور صحيحة / Password correct: " . ($passwordMatch ? '✅' : '❌') . "<br>";
            echo "- الحساب نشط / Account active: " . ($accountActive ? '✅' : '❌') . "<br>";
            echo "- الحساب غير محذوف / Not deleted: " . ($notDeleted ? '✅' : '❌') . "<br>";
            
            if ($passwordMatch && $accountActive && $notDeleted) {
                echo "<div style='color: green; font-weight: bold;'>✅ يمكن تسجيل الدخول بهذه البيانات / Can login with these credentials</div>";
            } else {
                echo "<div style='color: red; font-weight: bold;'>❌ لا يمكن تسجيل الدخول بهذه البيانات / Cannot login with these credentials</div>";
            }
        } else {
            echo "<div style='color: red;'>❌ المستخدم غير موجود / User not found</div>";
        }
        echo "<br>";
    }
    
    // 4. عرض حالة جميع الحسابات
    echo "<h3>4. حالة جميع الحسابات / All Accounts Status</h3>";
    
    $stmt = $pdo->query("SELECT id, first_name, last_name, email, role, account_status, deleted, created_on, last_login FROM admin ORDER BY id");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background-color: white;'>";
    echo "<tr>";
    echo "<th>ID</th>";
    echo "<th>الاسم / Name</th>";
    echo "<th>البريد الإلكتروني / Email</th>";
    echo "<th>الدور / Role</th>";
    echo "<th>الحالة / Status</th>";
    echo "<th>آخر دخول / Last Login</th>";
    echo "</tr>";
    
    foreach ($admins as $admin) {
        $canLogin = ($admin['account_status'] == '1' && $admin['deleted'] == '0');
        $statusColor = $canLogin ? 'green' : 'red';
        $statusText = $canLogin ? 'يمكن الدخول/Can Login' : 'لا يمكن الدخول/Cannot Login';
        
        echo "<tr>";
        echo "<td>{$admin['id']}</td>";
        echo "<td>{$admin['first_name']} {$admin['last_name']}</td>";
        echo "<td>{$admin['email']}</td>";
        echo "<td>{$admin['role']}</td>";
        echo "<td style='color: $statusColor; font-weight: bold;'>$statusText</td>";
        echo "<td>{$admin['last_login']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 5. إنشاء حساب طوارئ
    echo "<br><h3>5. إنشاء حساب طوارئ / Create Emergency Account</h3>";
    
    $emergencyEmail = '<EMAIL>';
    $emergencyPassword = 'emergency123';
    $emergencyHashedPassword = password_hash($emergencyPassword, PASSWORD_BCRYPT);
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin WHERE email = ?");
    $stmt->execute([$emergencyEmail]);
    $exists = $stmt->fetchColumn();
    
    if ($exists == 0) {
        $stmt = $pdo->prepare("INSERT INTO admin (first_name, last_name, email, password, role, mobile1, mobile2, created_on, account_status, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), '1', '0')");
        $result = $stmt->execute([
            'Emergency',
            'Admin',
            $emergencyEmail,
            $emergencyHashedPassword,
            'Super',
            '***********',
            '***********'
        ]);
        
        if ($result) {
            echo "<div style='color: green; font-weight: bold;'>";
            echo "✅ تم إنشاء حساب الطوارئ بنجاح! / Emergency account created successfully!<br>";
            echo "البريد الإلكتروني / Email: <strong>$emergencyEmail</strong><br>";
            echo "كلمة المرور / Password: <strong>$emergencyPassword</strong><br>";
            echo "</div>";
        }
    } else {
        echo "<div style='color: orange;'>⚠️ حساب الطوارئ موجود مسبقاً / Emergency account already exists</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>";
    echo "❌ خطأ في قاعدة البيانات! / Database Error!<br>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "</div>";
}

echo "<hr>";
echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin-top: 20px;'>";
echo "<strong>ملخص بيانات الدخول / Login Credentials Summary:</strong><br>";
echo "1. <EMAIL> / ********<br>";
echo "2. <EMAIL> / emergency123<br>";
echo "<br>";
echo "<strong>خطوات تسجيل الدخول / Login Steps:</strong><br>";
echo "1. اذهب إلى صفحة تسجيل الدخول / Go to login page<br>";
echo "2. أدخل البريد الإلكتروني وكلمة المرور / Enter email and password<br>";
echo "3. انقر على تسجيل الدخول / Click login<br>";
echo "<br>";
echo "<strong>⚠️ احذف هذا الملف بعد حل المشكلة لأسباب أمنية!</strong><br>";
echo "<strong>⚠️ Delete this file after fixing the issue for security reasons!</strong><br>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
table {
    background-color: white;
    margin: 10px 0;
}
th {
    background-color: #007bff;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
</style>
